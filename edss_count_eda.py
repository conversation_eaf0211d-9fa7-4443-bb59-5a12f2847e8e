"""
Exploratory Data Analysis: EDSS Count vs Patient Discontinuation Status
=======================================================================

This script performs a comprehensive exploratory data analysis to understand
the relationship between edss_count (number of EDSS assessments per patient)
and patient_status (active vs discontinued) in the Ocrevus therapy dataset.

EDSS (Expanded Disability Status Scale) is a method of quantifying disability
in multiple sclerosis. The frequency of EDSS assessments may indicate:
- Disease monitoring intensity
- Disease severity/progression
- Patient engagement with care
- Potential predictor for therapy discontinuation

Author: AI Assistant
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, mannwhitneyu
import warnings
warnings.filterwarnings('ignore')

def load_and_preprocess_data(file_path='Ocrevus_switch_alerts.csv'):
    """
    Load the dataset and perform initial preprocessing.

    Parameters:
    -----------
    file_path : str
        Path to the CSV file containing the Ocrevus data

    Returns:
    --------
    pd.DataFrame
        Preprocessed dataframe with clean edss_count and patient_status columns
    """
    print("=" * 70)
    print("OCREVUS THERAPY: EDSS COUNT vs DISCONTINUATION ANALYSIS")
    print("=" * 70)

    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None

    # Check for required columns
    required_cols = ['edss_count', 'patientsubstatus']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"❌ Error: Missing required columns: {missing_cols}")
        return None

    print(f"✓ Required columns found: {required_cols}")

    # Data preprocessing
    print("\n" + "-" * 50)
    print("DATA PREPROCESSING")
    print("-" * 50)

    # 1. Ensure edss_count is numeric
    original_dtype = df['edss_count'].dtype
    print(f"Original edss_count data type: {original_dtype}")

    # Convert to numeric, handling any non-numeric values
    df['edss_count'] = pd.to_numeric(df['edss_count'], errors='coerce')

    # Check for missing values after conversion
    missing_edss = df['edss_count'].isna().sum()
    total_rows = len(df)
    print(f"Missing values in edss_count: {missing_edss:,} ({missing_edss/total_rows*100:.1f}%)")

    if missing_edss > 0:
        print(f"⚠️  Warning: {missing_edss} missing values in edss_count")
        # Remove rows with missing edss_count as per requirements
        df = df.dropna(subset=['edss_count'])
        print(f"✓ Removed rows with missing edss_count. New shape: {df.shape}")
    else:
        print("✓ No missing values in edss_count")

    # 2. Simplify patientsubstatus to Active/Discontinued
    print(f"\nOriginal patientsubstatus categories: {df['patientsubstatus'].nunique()}")
    print("Sample categories:", df['patientsubstatus'].unique()[:5])

    # Create simplified status
    df['patient_status'] = df['patientsubstatus'].apply(
        lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active'
    )

    print(f"✓ Simplified to binary status:")
    status_counts = df['patient_status'].value_counts()
    for status, count in status_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   • {status}: {count:,} patients ({percentage:.1f}%)")

    # 3. Basic data validation
    print(f"\n✓ Final dataset shape: {df.shape}")
    print(f"✓ EDSS count range: {df['edss_count'].min():.0f} - {df['edss_count'].max():.0f}")

    return df

def analyze_edss_count_distribution(df):
    """
    Analyze the overall distribution of edss_count.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("EDSS COUNT: OVERALL DISTRIBUTION ANALYSIS")
    print("-" * 50)

    # Calculate descriptive statistics
    stats_overall = df['edss_count'].describe()

    print("📊 DESCRIPTIVE STATISTICS:")
    print(f"   • Count:          {stats_overall['count']:>8.0f}")
    print(f"   • Mean:           {stats_overall['mean']:>8.2f}")
    print(f"   • Median:         {stats_overall['50%']:>8.2f}")
    print(f"   • Standard Dev:   {stats_overall['std']:>8.2f}")
    print(f"   • Minimum:        {stats_overall['min']:>8.0f}")
    print(f"   • Maximum:        {stats_overall['max']:>8.0f}")
    print(f"   • 25th Percentile:{stats_overall['25%']:>8.2f}")
    print(f"   • 75th Percentile:{stats_overall['75%']:>8.2f}")

    # Additional statistics
    mode_val = df['edss_count'].mode().iloc[0] if not df['edss_count'].mode().empty else "N/A"
    skewness = df['edss_count'].skew()
    kurtosis = df['edss_count'].kurtosis()

    print(f"\n📈 DISTRIBUTION CHARACTERISTICS:")
    print(f"   • Mode:           {mode_val:>8}")
    print(f"   • Skewness:       {skewness:>8.3f} ({'Right-skewed' if skewness > 0.5 else 'Left-skewed' if skewness < -0.5 else 'Approximately symmetric'})")
    print(f"   • Kurtosis:       {kurtosis:>8.3f} ({'Heavy-tailed' if kurtosis > 0 else 'Light-tailed'})")

    # Value counts for discrete analysis
    print(f"\n📋 EDSS COUNT FREQUENCY DISTRIBUTION:")
    value_counts = df['edss_count'].value_counts().sort_index()
    total_patients = len(df)

    for count, freq in value_counts.head(10).items():
        percentage = (freq / total_patients) * 100
        print(f"   • {count:>2.0f} assessments: {freq:>5,} patients ({percentage:>5.1f}%)")

    if len(value_counts) > 10:
        remaining = len(value_counts) - 10
        print(f"   • ... and {remaining} more unique values")

def compare_by_patient_status(df):
    """
    Compare edss_count between active and discontinued patients.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe

    Returns:
    --------
    dict
        Dictionary containing statistical comparison results
    """
    print("\n" + "-" * 50)
    print("EDSS COUNT: COMPARISON BY PATIENT STATUS")
    print("-" * 50)

    # Calculate descriptive statistics by status
    stats_by_status = df.groupby('patient_status')['edss_count'].describe()

    print("📊 DESCRIPTIVE STATISTICS BY PATIENT STATUS:")
    print(stats_by_status.round(2))

    # Additional statistics by group
    print(f"\n📈 ADDITIONAL STATISTICS BY STATUS:")
    for status in df['patient_status'].unique():
        subset = df[df['patient_status'] == status]['edss_count']
        mode_val = subset.mode().iloc[0] if not subset.mode().empty else "N/A"
        skewness = subset.skew()

        print(f"\n{status} Patients:")
        print(f"   • Mode:           {mode_val:>8}")
        print(f"   • Skewness:       {skewness:>8.3f}")
        print(f"   • IQR:            {subset.quantile(0.75) - subset.quantile(0.25):>8.2f}")

    # Statistical significance testing
    print(f"\n🔬 STATISTICAL SIGNIFICANCE TESTING:")

    active_edss = df[df['patient_status'] == 'Active']['edss_count']
    discontinued_edss = df[df['patient_status'] == 'Discontinued']['edss_count']

    # Mann-Whitney U test (non-parametric)
    statistic, p_value = mannwhitneyu(active_edss, discontinued_edss, alternative='two-sided')

    print(f"Mann-Whitney U Test:")
    print(f"   • Test Statistic: {statistic:>10,.0f}")
    print(f"   • P-value:        {p_value:>10.6f}")
    print(f"   • Significance:   {'Significant' if p_value < 0.05 else 'Not significant'} (α = 0.05)")

    # Effect size (Cohen's d approximation)
    pooled_std = np.sqrt(((len(active_edss) - 1) * active_edss.var() +
                         (len(discontinued_edss) - 1) * discontinued_edss.var()) /
                        (len(active_edss) + len(discontinued_edss) - 2))
    cohens_d = (active_edss.mean() - discontinued_edss.mean()) / pooled_std

    print(f"   • Effect Size (Cohen's d): {cohens_d:>6.3f}")
    effect_interpretation = "Small" if abs(cohens_d) < 0.5 else "Medium" if abs(cohens_d) < 0.8 else "Large"
    print(f"   • Effect Interpretation: {effect_interpretation}")

    return {
        'stats_by_status': stats_by_status,
        'mann_whitney_statistic': statistic,
        'mann_whitney_p_value': p_value,
        'cohens_d': cohens_d,
        'active_mean': active_edss.mean(),
        'discontinued_mean': discontinued_edss.mean()
    }

def analyze_discontinuation_patterns(df):
    """
    Analyze detailed discontinuation patterns by EDSS count.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("DISCONTINUATION PATTERNS BY EDSS COUNT")
    print("-" * 50)

    # Create cross-tabulation
    crosstab = pd.crosstab(df['edss_count'], df['patient_status'], margins=True)

    print("📋 CROSS-TABULATION: EDSS COUNT vs PATIENT STATUS")
    print(crosstab)

    # Calculate discontinuation rates by EDSS count
    print(f"\n📊 DISCONTINUATION RATES BY EDSS COUNT:")

    discontinuation_rates = []
    for edss_count in sorted(df['edss_count'].unique()):
        subset = df[df['edss_count'] == edss_count]
        total_patients = len(subset)
        discontinued_patients = len(subset[subset['patient_status'] == 'Discontinued'])

        if total_patients >= 10:  # Only show rates for groups with sufficient sample size
            rate = (discontinued_patients / total_patients) * 100
            discontinuation_rates.append({
                'edss_count': edss_count,
                'total_patients': total_patients,
                'discontinued': discontinued_patients,
                'rate': rate
            })
            print(f"   • {edss_count:>2.0f} assessments: {discontinued_patients:>3}/{total_patients:>4} patients ({rate:>5.1f}%)")

    # Chi-square test for independence
    print(f"\n🔬 CHI-SQUARE TEST FOR INDEPENDENCE:")

    # Remove margin row/column for chi-square test
    crosstab_test = pd.crosstab(df['edss_count'], df['patient_status'])
    chi2, p_value_chi2, dof, expected = chi2_contingency(crosstab_test)

    print(f"   • Chi-square statistic: {chi2:>8.3f}")
    print(f"   • Degrees of freedom:   {dof:>8}")
    print(f"   • P-value:              {p_value_chi2:>8.6f}")
    print(f"   • Significance:         {'Significant' if p_value_chi2 < 0.05 else 'Not significant'} (α = 0.05)")

    # Identify high-risk and low-risk EDSS count groups
    if discontinuation_rates:
        rates_df = pd.DataFrame(discontinuation_rates)
        overall_rate = (len(df[df['patient_status'] == 'Discontinued']) / len(df)) * 100

        print(f"\n🎯 RISK STRATIFICATION (Overall rate: {overall_rate:.1f}%):")

        high_risk = rates_df[rates_df['rate'] > overall_rate + 5]
        low_risk = rates_df[rates_df['rate'] < overall_rate - 5]

        if not high_risk.empty:
            print(f"   🔴 HIGH-RISK EDSS COUNT GROUPS (>5% above average):")
            for _, row in high_risk.iterrows():
                print(f"      • {row['edss_count']:.0f} assessments: {row['rate']:.1f}% discontinuation rate")

        if not low_risk.empty:
            print(f"   🟢 LOW-RISK EDSS COUNT GROUPS (>5% below average):")
            for _, row in low_risk.iterrows():
                print(f"      • {row['edss_count']:.0f} assessments: {row['rate']:.1f}% discontinuation rate")

def create_comprehensive_visualization(df):
    """
    Create comprehensive visualizations for EDSS count analysis.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("CREATING COMPREHENSIVE VISUALIZATIONS")
    print("-" * 50)

    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")

    # Create a figure with multiple subplots
    fig = plt.figure(figsize=(20, 16))

    # 1. Overall EDSS Count Distribution (Histogram)
    plt.subplot(3, 3, 1)
    plt.hist(df['edss_count'], bins=range(int(df['edss_count'].min()), int(df['edss_count'].max()) + 2),
             alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('Overall EDSS Count Distribution', fontsize=14, fontweight='bold')
    plt.xlabel('Number of EDSS Assessments')
    plt.ylabel('Number of Patients')
    plt.grid(True, alpha=0.3)

    # 2. EDSS Count by Patient Status (Box Plot)
    plt.subplot(3, 3, 2)
    sns.boxplot(data=df, x='patient_status', y='edss_count', palette=['lightcoral', 'lightblue'])
    plt.title('EDSS Count Distribution by Patient Status', fontsize=14, fontweight='bold')
    plt.xlabel('Patient Status')
    plt.ylabel('Number of EDSS Assessments')

    # 3. EDSS Count by Patient Status (Violin Plot)
    plt.subplot(3, 3, 3)
    sns.violinplot(data=df, x='patient_status', y='edss_count', palette=['lightcoral', 'lightblue'])
    plt.title('EDSS Count Distribution (Violin Plot)', fontsize=14, fontweight='bold')
    plt.xlabel('Patient Status')
    plt.ylabel('Number of EDSS Assessments')

    # 4. Stacked Bar Chart: EDSS Count vs Patient Status
    plt.subplot(3, 3, 4)
    crosstab = pd.crosstab(df['edss_count'], df['patient_status'])
    crosstab.plot(kind='bar', stacked=True, ax=plt.gca(), color=['lightcoral', 'lightblue'])
    plt.title('Patient Status by EDSS Count (Stacked)', fontsize=14, fontweight='bold')
    plt.xlabel('Number of EDSS Assessments')
    plt.ylabel('Number of Patients')
    plt.legend(title='Patient Status')
    plt.xticks(rotation=45)

    # 5. Discontinuation Rate by EDSS Count
    plt.subplot(3, 3, 5)
    discontinuation_rates = []
    edss_counts = []

    for edss_count in sorted(df['edss_count'].unique()):
        subset = df[df['edss_count'] == edss_count]
        if len(subset) >= 5:  # Only include groups with at least 5 patients
            rate = (len(subset[subset['patient_status'] == 'Discontinued']) / len(subset)) * 100
            discontinuation_rates.append(rate)
            edss_counts.append(edss_count)

    plt.bar(edss_counts, discontinuation_rates, color='orange', alpha=0.7)
    plt.title('Discontinuation Rate by EDSS Count', fontsize=14, fontweight='bold')
    plt.xlabel('Number of EDSS Assessments')
    plt.ylabel('Discontinuation Rate (%)')
    plt.grid(True, alpha=0.3)

    # 6. Grouped Bar Chart: Count by Status and EDSS Count
    plt.subplot(3, 3, 6)
    crosstab_pct = pd.crosstab(df['edss_count'], df['patient_status'], normalize='index') * 100
    crosstab_pct.plot(kind='bar', ax=plt.gca(), color=['lightcoral', 'lightblue'])
    plt.title('Patient Status Distribution by EDSS Count (%)', fontsize=14, fontweight='bold')
    plt.xlabel('Number of EDSS Assessments')
    plt.ylabel('Percentage of Patients')
    plt.legend(title='Patient Status')
    plt.xticks(rotation=45)

    # 7. Histogram Comparison: Active vs Discontinued
    plt.subplot(3, 3, 7)
    active_edss = df[df['patient_status'] == 'Active']['edss_count']
    discontinued_edss = df[df['patient_status'] == 'Discontinued']['edss_count']

    plt.hist([active_edss, discontinued_edss], bins=range(int(df['edss_count'].min()), int(df['edss_count'].max()) + 2),
             alpha=0.7, label=['Active', 'Discontinued'], color=['lightblue', 'lightcoral'])
    plt.title('EDSS Count Distribution Comparison', fontsize=14, fontweight='bold')
    plt.xlabel('Number of EDSS Assessments')
    plt.ylabel('Number of Patients')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 8. Mean EDSS Count by Status (Bar Chart)
    plt.subplot(3, 3, 8)
    mean_by_status = df.groupby('patient_status')['edss_count'].mean()
    bars = plt.bar(mean_by_status.index, mean_by_status.values, color=['lightblue', 'lightcoral'])
    plt.title('Mean EDSS Count by Patient Status', fontsize=14, fontweight='bold')
    plt.xlabel('Patient Status')
    plt.ylabel('Mean Number of EDSS Assessments')

    # Add value labels on bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.2f}', ha='center', va='bottom', fontweight='bold')

    # 9. Summary Statistics Table (as text)
    plt.subplot(3, 3, 9)
    plt.axis('off')

    # Create summary statistics text
    stats_by_status = df.groupby('patient_status')['edss_count'].agg(['count', 'mean', 'median', 'std']).round(2)

    summary_text = "SUMMARY STATISTICS\n" + "="*25 + "\n\n"
    for status in stats_by_status.index:
        summary_text += f"{status} Patients:\n"
        summary_text += f"  Count: {stats_by_status.loc[status, 'count']:.0f}\n"
        summary_text += f"  Mean:  {stats_by_status.loc[status, 'mean']:.2f}\n"
        summary_text += f"  Median: {stats_by_status.loc[status, 'median']:.2f}\n"
        summary_text += f"  Std Dev: {stats_by_status.loc[status, 'std']:.2f}\n\n"

    plt.text(0.1, 0.9, summary_text, transform=plt.gca().transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

    plt.tight_layout()
    plt.savefig('edss_count_analysis.png', dpi=300, bbox_inches='tight')
    print("✓ Comprehensive visualization saved as 'edss_count_analysis.png'")

    return fig

def generate_insights_and_recommendations(df, stats_results):
    """
    Generate actionable insights and recommendations based on the analysis.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    stats_results : dict
        Dictionary containing statistical test results
    """
    print("\n" + "=" * 70)
    print("INSIGHTS AND RECOMMENDATIONS")
    print("=" * 70)

    # Calculate key metrics
    total_patients = len(df)
    active_patients = len(df[df['patient_status'] == 'Active'])
    discontinued_patients = len(df[df['patient_status'] == 'Discontinued'])
    overall_discontinuation_rate = (discontinued_patients / total_patients) * 100

    active_mean_edss = stats_results['active_mean']
    discontinued_mean_edss = stats_results['discontinued_mean']
    effect_size = abs(stats_results['cohens_d'])
    p_value = stats_results['mann_whitney_p_value']

    print("🔍 KEY FINDINGS:")
    print(f"   • Total patients analyzed: {total_patients:,}")
    print(f"   • Overall discontinuation rate: {overall_discontinuation_rate:.1f}%")
    print(f"   • Mean EDSS count (Active): {active_mean_edss:.2f}")
    print(f"   • Mean EDSS count (Discontinued): {discontinued_mean_edss:.2f}")
    print(f"   • Difference in means: {abs(active_mean_edss - discontinued_mean_edss):.2f}")
    print(f"   • Statistical significance: {'Yes' if p_value < 0.05 else 'No'} (p = {p_value:.6f})")
    print(f"   • Effect size: {effect_size:.3f} ({'Small' if effect_size < 0.5 else 'Medium' if effect_size < 0.8 else 'Large'})")

    print(f"\n💡 CLINICAL INTERPRETATION:")

    if p_value < 0.05:
        if active_mean_edss > discontinued_mean_edss:
            print("   • Active patients have significantly MORE EDSS assessments than discontinued patients")
            print("   • This suggests that regular monitoring may be associated with therapy continuation")
            print("   • Higher EDSS assessment frequency could indicate better patient engagement")
        else:
            print("   • Discontinued patients had significantly MORE EDSS assessments than active patients")
            print("   • This may indicate more intensive monitoring due to disease progression")
            print("   • Higher assessment frequency might signal clinical deterioration leading to discontinuation")
    else:
        print("   • No statistically significant difference in EDSS assessment frequency between groups")
        print("   • EDSS count alone may not be a strong predictor of discontinuation")
        print("   • Other factors may be more important for predicting therapy outcomes")

    print(f"\n🎯 PREDICTIVE VALUE ASSESSMENT:")

    # Calculate EDSS count categories for risk assessment
    edss_categories = []
    for edss_count in sorted(df['edss_count'].unique()):
        subset = df[df['edss_count'] == edss_count]
        if len(subset) >= 10:
            disc_rate = (len(subset[subset['patient_status'] == 'Discontinued']) / len(subset)) * 100
            edss_categories.append({
                'edss_count': edss_count,
                'discontinuation_rate': disc_rate,
                'sample_size': len(subset)
            })

    if edss_categories:
        edss_df = pd.DataFrame(edss_categories)
        high_risk_threshold = overall_discontinuation_rate + 10
        low_risk_threshold = overall_discontinuation_rate - 10

        high_risk_counts = edss_df[edss_df['discontinuation_rate'] > high_risk_threshold]
        low_risk_counts = edss_df[edss_df['discontinuation_rate'] < low_risk_threshold]

        if not high_risk_counts.empty or not low_risk_counts.empty:
            print("   • EDSS count shows potential as a discontinuation risk indicator:")

            if not high_risk_counts.empty:
                print(f"     - High-risk EDSS counts (>{high_risk_threshold:.1f}% discontinuation):")
                for _, row in high_risk_counts.iterrows():
                    print(f"       * {row['edss_count']:.0f} assessments: {row['discontinuation_rate']:.1f}% discontinuation rate")

            if not low_risk_counts.empty:
                print(f"     - Low-risk EDSS counts (<{low_risk_threshold:.1f}% discontinuation):")
                for _, row in low_risk_counts.iterrows():
                    print(f"       * {row['edss_count']:.0f} assessments: {row['discontinuation_rate']:.1f}% discontinuation rate")
        else:
            print("   • EDSS count shows limited predictive value for discontinuation risk")

    print(f"\n📋 ACTIONABLE RECOMMENDATIONS:")

    print("   1. CLINICAL MONITORING:")
    if active_mean_edss > discontinued_mean_edss:
        print("      • Encourage regular EDSS assessments to improve therapy retention")
        print("      • Implement monitoring protocols for patients with low assessment frequency")
        print("      • Consider patient engagement programs to increase monitoring compliance")
    else:
        print("      • Investigate reasons for high EDSS assessment frequency in discontinued patients")
        print("      • Develop early intervention strategies for patients requiring frequent monitoring")
        print("      • Consider alternative monitoring approaches for high-risk patients")

    print("   2. RISK STRATIFICATION:")
    if effect_size >= 0.3:  # Meaningful effect size
        print("      • Include EDSS assessment frequency in discontinuation risk models")
        print("      • Develop EDSS count-based patient stratification protocols")
        print("      • Create targeted interventions based on assessment frequency patterns")
    else:
        print("      • EDSS count alone insufficient for risk stratification")
        print("      • Combine with other clinical and demographic factors")
        print("      • Focus on EDSS score values rather than frequency")

    print("   3. PATIENT MANAGEMENT:")
    print("      • Standardize EDSS assessment schedules across patient populations")
    print("      • Train healthcare providers on optimal assessment timing")
    print("      • Implement patient education programs about the importance of regular monitoring")

    print("   4. FURTHER RESEARCH:")
    print("      • Investigate relationship between EDSS scores (values) and discontinuation")
    print("      • Analyze temporal patterns of EDSS assessments before discontinuation")
    print("      • Study combined effects of EDSS count with other clinical variables")
    print("      • Validate findings in independent patient cohorts")

def main():
    """
    Main function to run the complete EDA analysis.
    """
    # Load and preprocess data
    df = load_and_preprocess_data()

    if df is None:
        print("❌ Analysis terminated due to data loading issues.")
        return

    # Perform analysis steps
    analyze_edss_count_distribution(df)
    stats_results = compare_by_patient_status(df)
    analyze_discontinuation_patterns(df)
    create_comprehensive_visualization(df)
    generate_insights_and_recommendations(df, stats_results)

    print("\n" + "=" * 70)
    print("ANALYSIS COMPLETE")
    print("=" * 70)
    print("✓ Comprehensive EDSS Count EDA completed successfully")
    print("✓ Visualization saved as 'edss_count_analysis.png'")
    print("✓ Statistical analysis and insights generated")
    print("✓ Ready for clinical interpretation and decision-making")

if __name__ == "__main__":
    main()
